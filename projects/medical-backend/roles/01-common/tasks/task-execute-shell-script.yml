---
# Ensure facts are gathered (especially for ansible_date_time)
- name: Gather facts if not already available
  ansible.builtin.setup:
    gather_subset:
      - "min"
      - "date_time"
  when: ansible_date_time is not defined

# Ensure remote directories exist
- name: Create remote scripts directory
  ansible.builtin.file:
    path: "{{ remote_scripts_dir }}"
    state: directory
    mode: "0755"
  become: false

# Copy base shell scripts to remote hosts first
- name: Copy base shell scripts to remote hosts
  ansible.builtin.copy:
    src: "{{ item }}"
    dest: "{{ remote_scripts_dir }}/{{ item }}"
    mode: "0755"
    backup: true
  loop: "{{ MEDICAL.scripts.base_shells }}"
  register: base_scripts_copy_result
  when:
    - MEDICAL.scripts.base_shells is defined
    - MEDICAL.scripts.base_shells | length > 0
    - sync_base_shells | default(true) | bool

# Copy main script to remote hosts
- name: Copy main script to remote hosts
  ansible.builtin.copy:
    src: "{{ script_name }}"
    dest: "{{ remote_scripts_dir }}/{{ script_name }}"
    mode: "0755"
    backup: true
  register: script_copy_result

# Copy relative csv file to remote hosts
- name: Copy relative csv file to remote hosts
  vars:
    script_name_without_ext: "{{ script_name | regex_replace('\\..*$', '') }}"
  ansible.builtin.copy:
    src: "{{ script_name_without_ext }}.csv"
    dest: "{{ remote_scripts_dir }}/{{ script_name_without_ext }}.csv"
    mode: "0644"
    backup: true
  register: csv_copy_result
  when: script_name_without_ext in MEDICAL.scripts.relative_local_csv_file_tags

# Get current timestamp as fallback if ansible_date_time is not available
- name: Get current timestamp
  ansible.builtin.command: date -u +"%Y-%m-%dT%H:%M:%SZ"
  register: current_timestamp
  changed_when: false
  when: ansible_date_time is not defined

# Create execution log
- name: Create execution log entry
  ansible.builtin.lineinfile:
    path: "{{ remote_scripts_dir }}/execution.log"
    line: >-
      {{ ansible_date_time.iso8601 | default(current_timestamp.stdout) }} -
      {{ ansible_hostname | default(inventory_hostname) }} -
      {{ script_name }} - STARTED
    create: true
    mode: "0644"
  become: false

# Execute the script with proper error handling
- name: Execute shell script
  vars:
    timeout_cmd: "{% if MEDICAL.scripts.execution_timeout | int != -1 %}timeout {{ MEDICAL.scripts.execution_timeout }}{% else %}{% endif %}"
    use_timeout: "{{ MEDICAL.scripts.execution_timeout | int != -1 }}"
  ansible.builtin.shell: >
    set -euo pipefail;
    cd "{{ remote_scripts_dir }}";
    TIMESTAMP="{{ ansible_date_time.epoch | default(lookup('pipe', 'date +%s')) }}";
    LOG_FILE="{{ remote_scripts_dir }}/{{ script_name }}_${TIMESTAMP}.log";
    {% if use_timeout %}
    if {{ timeout_cmd }} bash "{{ remote_scripts_dir }}/{{ script_name }}" "{{ release_tag }}" 2>&1 | tee "$LOG_FILE"; then
    {% else %}
    if bash "{{ remote_scripts_dir }}/{{ script_name }}" "{{ release_tag }}" 2>&1 | tee "$LOG_FILE"; then
    {% endif %}
      SCRIPT_EXIT_CODE=0;
      echo "Script executed successfully";
    else
      SCRIPT_EXIT_CODE=$?;
      echo "Script execution failed with exit code: $SCRIPT_EXIT_CODE";
    fi;
    exit $SCRIPT_EXIT_CODE
  register: script_execution_result
  become: false
  args:
    executable: /bin/bash
  failed_when: script_execution_result.rc != 0
  changed_when: script_execution_result.rc == 0

# Log successful execution
- name: Log successful execution
  ansible.builtin.lineinfile:
    path: "{{ remote_scripts_dir }}/execution.log"
    line: >-
      {{ ansible_date_time.iso8601 | default(current_timestamp.stdout) | default(lookup('pipe', 'date -u +"%Y-%m-%dT%H:%M:%SZ"')) }} -
      {{ ansible_hostname | default(inventory_hostname) }} -
      {{ script_name }} - SUCCESS -
      Exit Code: {{ script_execution_result.rc }}
    mode: "0644"
  become: false
  when: script_execution_result.rc == 0

# Log failed execution
- name: Log failed execution
  ansible.builtin.lineinfile:
    path: "{{ remote_scripts_dir }}/execution.log"
    line: >-
      {{ ansible_date_time.iso8601 | default(current_timestamp.stdout) | default(lookup('pipe', 'date -u +"%Y-%m-%dT%H:%M:%SZ"')) }} -
      {{ ansible_hostname | default(inventory_hostname) }} -
      {{ script_name }} - FAILED -
      Exit Code: {{ script_execution_result.rc }}
    mode: "0644"
  become: false
  when: script_execution_result.rc != 0

# Display execution results
- name: Display script execution results
  ansible.builtin.debug:
    msg: |
      Script Execution Summary:
      ========================
      Script Name: {{ script_name }}
      Target Host: {{ ansible_hostname | default(inventory_hostname) }}
      Exit Code: {{ script_execution_result.rc }}
      Execution Time: {{ ansible_date_time.iso8601 | default(current_timestamp.stdout) | default(lookup('pipe', 'date -u +"%Y-%m-%dT%H:%M:%SZ"')) }}

      Script Output:
      {{ script_execution_result.stdout }}

      {% if script_execution_result.stderr %}
      Script Errors:
      {{ script_execution_result.stderr }}
      {% endif %}

      Log File: {{ remote_scripts_dir }}/{{ script_name }}_{{ ansible_date_time.epoch | default(lookup('pipe', 'date +%s')) }}.log

# Handle script execution failure
- name: Handle script execution failure
  ansible.builtin.fail:
    msg: |
      Script execution failed on host {{ ansible_hostname | default(inventory_hostname) }}:

      Script: {{ script_name }}
      Exit Code: {{ script_execution_result.rc }}
      Error Output: {{ script_execution_result.stderr | default('No error output') }}

      Check the detailed log at: {{ remote_scripts_dir }}/{{ script_name }}_{{ ansible_date_time.epoch | default(lookup('pipe', 'date +%s')) }}.log
  when: script_execution_result.rc != 0

# Clean up old script files (idempotency)
- name: Clean up old script executions (keep last 10)
  ansible.builtin.shell: |
    set -euo pipefail
    cd "{{ remote_scripts_dir }}"
    # Keep only the 10 most recent log files for this script
    ls -1t {{ script_name }}_*.log 2>/dev/null | tail -n +11 | xargs -r rm -f || true
  become: false
  changed_when: false
  failed_when: false
  args:
    executable: /bin/bash
